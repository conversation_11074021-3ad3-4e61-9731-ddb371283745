// GameDashboardScreen.js
import { renderGame, renderGameDOM } from '../game/renderer.js';
import { performanceEngine } from '../game/performanceEngine.js';
import { subscribeToState, sendAction, sendChat } from '../multiplayer/sync.js';
import { getState } from '../../msec/framework/state.js';
import { renderDOM } from '../../msec/framework/dom.js';
import { navigate } from '../../msec/framework/router.js';
import { gameEndScreen } from './gameEndScreen.js';
import { startNewSession } from '../multiplayer/socket.js';

let root = null;
let gameState = null;
let inputHandlersAttached = false;
let performanceInitialized = false;

export function GameDashboardScreen() {
  root = document.getElementById('app');
  console.log('GameDashboardScreen initialized');

  const vdom = {
    tag: 'div',
    attrs: { class: 'character-container' },
    children: [
        {
            tag: 'div',
            attrs: { class: 'main-menu' },
            children: [{
                tag: 'div',
                attrs: { class: 'menu-container' },
                children: [
                    {
                        tag: 'div',
                        attrs: { class: 'left-border-shape' },
                        children: [{
                            tag: 'img',
                            attrs: {
                              src: './static/images/left-border-edge.png',
                              alt: 'Left Border Shape'
                            },
                            children: [],
                        }]
                    },
                    {
                        tag: 'div',
                        attrs: { class: 'menu-content' },
                        children: [
                            {
                                tag: 'div',
                                attrs: { class: 'left-side' },
                                children: [
                                    { tag: 'a', attrs: { href: '#', id: 'main-menu-btn' }, children: ['Main Menu'] },
                                    { tag: 'div', attrs: { class: 'seperator' }, children: [] },
                                    { tag: 'p', attrs: { class: 'player-font' }, children: ['Player 1 (Opponent)'] },
                                    {
                                        tag: 'div',
                                        attrs: { class: 'hearts' },
                                        children: [
                                            { tag: 'div', attrs: { class: 'heart' }, children: [] },
                                            { tag: 'div', attrs: { class: 'heart' }, children: [] },
                                            { tag: 'div', attrs: { class: 'heart' }, children: [] }
                                        ]
                                    }
                                ]
                            },
                            {
                                tag: 'div',
                                attrs: { class: 'right-side' },
                                children: [
                                    {
                                        tag: 'div',
                                        attrs: { class: 'stat' },
                                        children: [{
                                            tag: 'div',
                                            attrs: { class: 'flex' },
                                            children: [
                                                { tag: 'img', attrs: { src: './static/images/Speed.png', alt: 'Speed Icon' }, children: [] },
                                                { tag: 'h3', attrs: { class: 'speed-stat' }, children: ['1'] } // Default value of 1
                                            ]
                                        }]
                                    },
                                    {
                                        tag: 'div',
                                        attrs: { class: 'stat' },
                                        children: [{
                                            tag: 'div',
                                            attrs: { class: 'flex' },
                                            children: [
                                                { tag: 'img', attrs: { src: './static/images/bomb.png', alt: 'Bomb Icon' }, children: [] },
                                                { tag: 'h3', attrs: { class: 'bomb-stat' }, children: ['1'] }
                                            ]
                                        }]
                                    },
                                    {
                                        tag: 'div',
                                        attrs: { class: 'stat' },
                                        children: [{
                                            tag: 'div',
                                            attrs: { class: 'flex' },
                                            children: [
                                                { tag: 'img', attrs: { src: './static/images/Fire.png', alt: 'Fire Icon' }, children: [] },
                                                { tag: 'h3', attrs: { class: 'fire-stat' }, children: ['1'] }
                                            ]
                                        }]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        tag: 'div',
                        attrs: { class: 'right-border-shape' },
                        children: [{
                            tag: 'img',
                            attrs: {
                              src: './static/images/left-border-edge.png',
                              alt: 'Left Border Shape'
                            },
                            children: [],
                        }]
                    }
                ]
            }]
        },
        // dashboard
        {
          tag: 'div',
          attrs: { class: 'dashboard' },
          children: [
            //chat
            {
              tag: 'div',
              attrs: { class: 'chat-container closed' },
              children: [
                {
                  tag: 'div',
                  attrs: { class: 'chat-drawer' },
                  children: [
                    { tag: 'p', attrs: { class: 'player-font' }, children: ['Chat'] }
                  ]
                },
                {
                  tag: 'div',
                  attrs: { class: 'chat-sidebar' },
                  children: [
                    {
                      tag: 'div',
                      attrs: { class: 'chat-messages' },
                      children: [
                        {
                          tag: 'div',
                          attrs: { class: 'chat-container-inner' },
                          children: Array(11).fill().map(() => ({
                            tag: 'div',
                            attrs: { class: 'chat-message' },
                            children: [
                              { tag: 'span', attrs: { class: 'player-font' }, children: ['Player1:'] },
                              { tag: 'p', children: ['Hello, ready to play?'] }
                            ]
                          }))
                        }
                      ]
                    },
                    {
                      tag: 'div',
                      attrs: { class: 'input-form' },
                      children: [
                        {
                          tag: 'form',
                          attrs: { action: '#', method: 'post' },
                          children: [
                            {
                              tag: 'input',
                              attrs: {
                                type: 'text',
                                placeholder: 'Message',
                                class: 'chat-input'
                              }
                            },
                            {
                              tag: 'button',
                              attrs: { type: 'submit', class: 'send-button' },
                              children: [
                                {
                                  tag: 'img',
                                  attrs: {
                                    src: './static/images/send.png',
                                    alt: 'Send Icon'
                                  }
                                }
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            // game area
            {
              tag: 'div',
              attrs: { class: 'game-area' },
              children: [
                {
                  tag: 'div',
                  attrs: { class: 'game-board', id: 'game-board' },
                  children: [] // Game content will be rendered here
                }
              ]
            },
            // player stats sidebar
            {
              tag: 'div',
              attrs: { class: 'player-stats-sidebar' },
              children: Array(3).fill().map(() => ({
                tag: 'div',
                attrs: { class: 'player-stat' },
                children: [
                  // player2
                  {
                    tag: 'div',
                    attrs: { class: 'player-name' },
                    children: [
                      { tag: 'h2', attrs: { class: 'player-font' }, children: ['Player 2'] },
                      {
                        tag: 'div',
                        attrs: { class: 'hearts' },
                        children: Array(3).fill().map(() => ({
                          tag: 'div',
                          attrs: { class: 'heart' }
                        }))
                      }
                    ]
                  },
                  //player3
                  {
                    tag: 'div',
                    attrs: { class: 'player-stats' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'stat' },
                        children: [
                          {
                            tag: 'div',
                            attrs: { class: 'flex' },
                            children: [
                              {
                                tag: 'img',
                                attrs: {
                                  src: './static/images/bomb.png',
                                  alt: 'Bomb Icon'
                                }
                              },
                              {
                                tag: 'h3',
                                attrs: { class: 'speed-stat' },
                                children: ['bomb: 1']
                              }
                            ]
                          }
                        ]
                      },
                      //player4
                      {
                        tag: 'div',
                        attrs: { class: 'stat' },
                        children: [
                          {
                            tag: 'div',
                            attrs: { class: 'flex' },
                            children: [
                              {
                                tag: 'img',
                                attrs: {
                                  src: './static/images/Fire.png',
                                  alt: 'Bomb Icon'
                                }
                              },
                              {
                                tag: 'h3',
                                attrs: { class: 'speed-stat' },
                                children: ['Flame: 3']
                              }
                            ]
                          }
                        ]
                      },
                      {
                        tag: 'div',
                        attrs: { class: 'stat' },
                        children: [
                          {
                            tag: 'div',
                            attrs: { class: 'flex' },
                            children: [
                              {
                                tag: 'img',
                                attrs: {
                                  src: './static/images/Speed.png',
                                  alt: 'Speed Icon'
                                }
                              },
                              {
                                tag: 'h3',
                                attrs: { class: 'speed-stat' },
                                children: ['Speed: 1']
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }))
            }
          ]
        },
        // player stats down
        {
          tag: 'div',
          attrs: { class: 'player-stats-down' },
          children: Array(3).fill().map(() => ({
            tag: 'div',
            attrs: { class: 'player-stat' },
            children: [
              {
                tag: 'div',
                attrs: { class: 'player-name' },
                children: [
                  { tag: 'h2', attrs: { class: 'player-font' }, children: ['Player 2'] },
                  {
                    tag: 'div',
                    attrs: { class: 'hearts' },
                    children: Array(3).fill().map(() => ({
                      tag: 'div',
                      attrs: { class: 'heart' },
                      children: [],
                    })),
                  },
                ],
              },
              {
                tag: 'div',
                attrs: { class: 'player-stats' },
                children: [
                  {
                    tag: 'div',
                    attrs: { class: 'stat' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'flex' },
                        children: [
                          { tag: 'img', attrs: { src: './static/images/bomb.png', alt: 'Bomb Icon' }, children: [] },
                          { tag: 'h3', attrs: { class: 'speed-stat' }, children: ['bomb: 1'] },
                        ],
                      },
                    ],
                  },
                  {
                    tag: 'div',
                    attrs: { class: 'stat' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'flex' },
                        children: [
                          { tag: 'img', attrs: { src: './static/images/Fire.png', alt: 'Bomb Icon' }, children: [] },
                          { tag: 'h3', attrs: { class: 'speed-stat' }, children: ['Flame: 1'] },
                        ],
                      },
                    ],
                  },
                  {
                    tag: 'div',
                    attrs: { class: 'stat' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'flex' },
                        children: [
                          { tag: 'img', attrs: { src: './static/images/Speed.png', alt: 'Speed Icon' }, children: [] },
                          { tag: 'h3', attrs: { class: 'speed-stat' }, children: ['Speed: 1'] },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          })),
        },

    ]
  };

  function setupChatToggle() {
    const chatDrawer = document.querySelector('.chat-drawer');
    const chatContainer = document.querySelector('.chat-container');
    if (chatDrawer && chatContainer) {
      chatDrawer.addEventListener('click', () => {
        chatContainer.classList.toggle('closed');
      });
    }
  }

  function setupChatInput() {
    const chatForm = document.querySelector('.input-form form');
    const chatInput = document.querySelector('.chat-input');

    if (chatForm && chatInput) {
      chatForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const message = chatInput.value.trim();
        if (message) {
          sendChat(message);
          chatInput.value = '';
        }
      });
    }
  }

  function setupMainMenuHandler() {
    const mainMenuBtn = document.getElementById('main-menu-btn');
    if (mainMenuBtn) {
      mainMenuBtn.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('[GameDashboard] Main menu clicked');
        
        // Send main menu action to server
        sendAction({ type: 'mainMenu' });

        // Clear session when going to main menu (allows new nickname)
        startNewSession();

        // Navigate to main menu using SPA navigation
        navigate('/');
      });
    }
  }

  function updatePlayerStats(players) {
    if (!players || !Array.isArray(players)) {
      console.log('[GameDashboard] updatePlayerStats called with invalid players:', players);
      return;
    }

    console.log('[GameDashboard] updatePlayerStats called with', players.length, 'players');

    // Find the current player
    const playerId = localStorage.getItem('playerId');
    const currentPlayer = players.find(p => p.id === playerId);

    console.log('[GameDashboard] Current player ID:', playerId);
    console.log('[GameDashboard] Found current player:', currentPlayer);
    
    // Get other players (excluding current player)
    const otherPlayers = players.filter(p => p.id !== playerId);

    // Update sidebar stats
    const sidebarStats = document.querySelectorAll('.player-stats-sidebar .player-stat');
    sidebarStats.forEach((statContainer, index) => {
        const player = otherPlayers[index];
        if (player) {
            // Update player name with opponent label
            const nameElement = statContainer.querySelector('.player-font');
            if (nameElement) {
                nameElement.textContent = `${player.nickname} (Opponent)`;
            }

            // Update hearts
            const hearts = statContainer.querySelectorAll('.heart');
            hearts.forEach((heart, idx) => {
                heart.style.opacity = idx < player.lives ? '1' : '0.3';
                heart.style.filter = idx < player.lives ? 'none' : 'grayscale(100%)';
            });

            // Update stats
            const bombStat = statContainer.querySelector('.stat:nth-child(1) .speed-stat');
            const flameStat = statContainer.querySelector('.stat:nth-child(2) .speed-stat');
            const speedStat = statContainer.querySelector('.stat:nth-child(3) .speed-stat');

            if (bombStat) bombStat.textContent = `Bombs: ${player.bombsCount}`;
            if (flameStat) flameStat.textContent = `Flame: ${player.flameRange}`;
            if (speedStat) speedStat.textContent = `Speed: ${player.speed}`;

            // Show container and update opacity based on alive status
            statContainer.style.display = 'block';
            statContainer.style.opacity = player.alive ? '1' : '0.5';
        } else {
            // Hide unused player slots
            statContainer.style.display = 'none';
        }
    });

    // Update bottom stats
    const bottomStats = document.querySelectorAll('.player-stats-down .player-stat');
    bottomStats.forEach((statContainer, index) => {
        const player = otherPlayers[index];
        if (player) {
            // Update player name with opponent label
            const nameElement = statContainer.querySelector('.player-font');
            if (nameElement) {
                nameElement.textContent = `${player.nickname} (Opponent)`;
            }

            // Update hearts
            const hearts = statContainer.querySelectorAll('.heart');
            hearts.forEach((heart, idx) => {
                heart.style.opacity = idx < player.lives ? '1' : '0.3';
                heart.style.filter = idx < player.lives ? 'none' : 'grayscale(100%)';
            });

            // Update stats
            const bombStat = statContainer.querySelector('.stat:nth-child(1) .speed-stat');
            const flameStat = statContainer.querySelector('.stat:nth-child(2) .speed-stat');
            const speedStat = statContainer.querySelector('.stat:nth-child(3) .speed-stat');

            if (bombStat) bombStat.textContent = `Bombs: ${player.bombsCount}`;
            if (flameStat) flameStat.textContent = `Flame: ${player.flameRange}`;
            if (speedStat) speedStat.textContent = `Speed: ${player.speed}`;

            // Show container and update opacity based on alive status
            statContainer.style.display = 'block';
            statContainer.style.opacity = player.alive ? '1' : '0.5';
        } else {
            // Hide unused player slots
            statContainer.style.display = 'none';
        }
    });

    // Update current player stats in top menu (if needed)
    if (currentPlayer) {
        const speedStat = document.querySelector('.right-side .speed-stat');
        const bombStat = document.querySelector('.right-side .bomb-stat');
        const fireStat = document.querySelector('.right-side .fire-stat');

        console.log('[GameDashboard] Updating stats for:', currentPlayer.nickname);
        console.log('[GameDashboard] Current player stats:', {
          speed: currentPlayer.speed,
          bombsCount: currentPlayer.bombsCount,
          flameRange: currentPlayer.flameRange
        });
        console.log('[GameDashboard] Found elements:', {
          speedStat: !!speedStat,
          bombStat: !!bombStat,
          fireStat: !!fireStat
        });

        if (speedStat) {
          speedStat.textContent = currentPlayer.speed.toString();
          // Add visual feedback for speed changes
          if (currentPlayer.speed > 1) {
            speedStat.style.color = '#4ECDC4'; // Highlight when speed is boosted
            speedStat.style.fontWeight = 'bold';
          } else {
            speedStat.style.color = '';
            speedStat.style.fontWeight = '';
          }
        }
        if (bombStat) bombStat.textContent = currentPlayer.bombsCount.toString();
        if (fireStat) {
          fireStat.textContent = currentPlayer.flameRange.toString();
          console.log('[GameDashboard] Updated fire stat to:', currentPlayer.flameRange);
        } else {
          console.warn('[GameDashboard] Fire stat element not found!');
        }

        // Update current player hearts
        const hearts = document.querySelectorAll('.left-side .heart');
        hearts.forEach((heart, index) => {
            heart.style.opacity = index < currentPlayer.lives ? '1' : '0.3';
            heart.style.filter = index < currentPlayer.lives ? 'none' : 'grayscale(100%)';
        });

        // Update current player name
        const playerName = document.querySelector('.left-side .player-font');
        if (playerName) {
            playerName.textContent = currentPlayer.nickname;
        }
    }
  }

  function updateChatMessages(chatMessages) {
    const chatContainer = document.querySelector('.chat-container-inner');
    if (!chatContainer || !chatMessages || !Array.isArray(chatMessages)) return;

    // Clear and rebuild chat messages
    chatContainer.innerHTML = '';

    // Show last 10 messages
    const recentMessages = chatMessages.slice(-10);
    recentMessages.forEach(msg => {
      const messageDiv = document.createElement('div');
      messageDiv.className = 'chat-message';

      const nicknameSpan = document.createElement('span');
      nicknameSpan.className = 'player-font';
      nicknameSpan.textContent = `${msg.nickname}:`;

      const textP = document.createElement('p');
      textP.textContent = msg.text;

      messageDiv.appendChild(nicknameSpan);
      messageDiv.appendChild(textP);
      chatContainer.appendChild(messageDiv);
    });

    // Auto-scroll to bottom
    chatContainer.scrollTop = chatContainer.scrollHeight;
  }

  function renderGameContent(state) {
    const gameBoard = document.getElementById('game-board');
    if (!gameBoard || !state) return;

    // Handle different game phases
    if (state.phase === 'lobby') {
      // Stop performance engine when leaving game
      if (performanceInitialized) {
        performanceEngine.stop();
        performanceInitialized = false;
      }
      navigate('/lobby');
      return;
    }

    if (state.phase === 'end') {
      // Stop performance engine when game ends
      if (performanceInitialized) {
        performanceEngine.stop();
        performanceInitialized = false;
      }
      gameEndScreen(root, state.winner, state.players || []);
      return;
    }

    if (state.phase === 'game' && state.map && Array.isArray(state.players) && state.players.length > 0) {
      // Initialize performance engine for game rendering only when we have valid game state
      if (!performanceInitialized) {
        performanceEngine.init(gameBoard, (gameState, deltaTime) => {
          // Performance engine callback for 60fps updates
          // This is called at 60fps for smooth rendering
          if (gameState && gameBoard && gameState.players && gameState.players.length > 0) {
            try {
              renderGame(gameState, gameBoard);
            } catch (error) {
              console.warn('[GameDashboard] Performance render failed:', error);
            }
          }
        });
        performanceEngine.start();
        performanceInitialized = true;
        console.log('[GameDashboard] Performance engine initialized for 60fps rendering');
      }

      // Update the performance engine with new state
      performanceEngine.updateState(state);

      // Clear previous content only once and ensure we have players
      if (gameBoard.children.length === 0) {
        try {
          // Initial render with complete state
          renderGame(state, gameBoard);

          // Add controls info
          const controlsInfo = document.createElement('div');
          controlsInfo.style.cssText = 'margin-top: 10px; text-align: center; color: #ccc; font-size: 12px;';
          controlsInfo.innerHTML = 'Use WASD or Arrow Keys to move, Space to place bombs | P for performance stats';
          gameBoard.appendChild(controlsInfo);

          // Add performance stats display
          const perfStats = document.createElement('div');
          perfStats.id = 'perf-stats';
          perfStats.style.cssText = 'margin-top: 5px; text-align: center; color: #999; font-size: 10px;';
          gameBoard.appendChild(perfStats);

          // Update performance stats every second
          setInterval(() => {
            const stats = performanceEngine.getPerformanceMetrics();
            const perfStatsEl = document.getElementById('perf-stats');
            if (perfStatsEl && stats) {
              const color = stats.fps >= 55 ? '#0f0' : stats.fps >= 30 ? '#ff0' : '#f00';
              perfStatsEl.innerHTML = `<span style="color: ${color}">FPS: ${stats.fps}</span> | Frame Time: ${stats.avgFrameTime || 'N/A'}ms`;
            }
          }, 1000);

        } catch (error) {
          console.warn('[GameDashboard] DOM rendering failed:', error);
        }
      }
    } else if (state.phase === 'game' && state.map && (!state.players || state.players.length === 0)) {
      // Show loading state if we have a map but no players yet
      gameBoard.innerHTML = `
        <div style="
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #fff;
          font-size: 18px;
          font-family: Arial, sans-serif;
          text-align: center;
          background: rgba(0, 0, 0, 0.8);
          padding: 20px;
          border-radius: 8px;
        ">
          Waiting for players to join...
        </div>
      `;
    }
  }

  function attachInputHandlers() {
    if (inputHandlersAttached) return;

    // Input handling with movement cooldown and speed tracking
    let keysPressed = new Set();
    let lastMoveTime = 0;
    let currentPlayerSpeed = 1;

    // Dynamic movement cooldown based on player speed
    function getMoveCooldown() {
      return Math.max(100, 200 / currentPlayerSpeed); // Faster players have shorter cooldown
    }

    const handleKeyDown = (e) => {
      // Don't handle input if typing in chat
      if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
        return;
      }

      // Track pressed keys for smooth movement
      keysPressed.add(e.key);

      let action = null;
      const now = Date.now();

      switch (e.key) {
        case 'ArrowUp': case 'w': case 'W':
          if (now - lastMoveTime >= getMoveCooldown()) {
            action = { type: 'move', dir: 'up' };
            lastMoveTime = now;
          }
          break;
        case 'ArrowDown': case 's': case 'S':
          if (now - lastMoveTime >= getMoveCooldown()) {
            action = { type: 'move', dir: 'down' };
            lastMoveTime = now;
          }
          break;
        case 'ArrowLeft': case 'a': case 'A':
          if (now - lastMoveTime >= getMoveCooldown()) {
            action = { type: 'move', dir: 'left' };
            lastMoveTime = now;
          }
          break;
        case 'ArrowRight': case 'd': case 'D':
          if (now - lastMoveTime >= getMoveCooldown()) {
            action = { type: 'move', dir: 'right' };
            lastMoveTime = now;
          }
          break;
        case ' ':
          action = { type: 'bomb' };
          break;
        case 'p': case 'P':
          // Show performance stats
          if (performanceInitialized) {
            performanceEngine.logPerformanceStats();
          }
          e.preventDefault();
          return;
      }

      if (action) {
        console.log('[GameDashboard] Sending action:', action);
        sendAction(action);
        e.preventDefault();
      }
    };

    const handleKeyUp = (e) => {
      keysPressed.delete(e.key);
    };

    // Update current player speed when state changes
    function updatePlayerSpeed(players) {
      if (players && Array.isArray(players)) {
        // Find current player (you can modify this logic based on how you identify current player)
        const currentPlayer = players.find(p => p.id === localStorage.getItem('playerId'));
        if (currentPlayer && currentPlayer.speed !== currentPlayerSpeed) {
          currentPlayerSpeed = currentPlayer.speed;
          console.log('[GameDashboard] Player speed updated to:', currentPlayerSpeed);
        }
      }
    }

    // Expose speed update function for state updates
    window.updatePlayerSpeedFromState = updatePlayerSpeed;

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    inputHandlersAttached = true;

    console.log('[GameDashboard] Input handlers attached with speed-based movement');
  }

  // Subscribe to state updates from server
  subscribeToState((state) => {
    gameState = state;
    console.log('[GameDashboard] State update:', state);

    // Update UI components
    updatePlayerStats(state.players);
    updateChatMessages(state.chat);
    renderGameContent(state);

    // Update player speed for input handling
    if (window.updatePlayerSpeedFromState) {
      window.updatePlayerSpeedFromState(state.players);
    }
  });

  renderDOM(vdom, root);
  setupChatToggle();
  setupChatInput();
  attachInputHandlers();
  setupMainMenuHandler();

  // Debug: Check if fire stat element exists after rendering
  setTimeout(() => {
    const fireStat = document.querySelector('.right-side .fire-stat');
    console.log('[GameDashboard] Fire stat element after render:', fireStat);
    if (fireStat) {
      console.log('[GameDashboard] Fire stat current text:', fireStat.textContent);
    }
  }, 100);

  // Don't try to render with framework state - wait for WebSocket state updates
  // The subscribeToState callback will handle the initial render when the first state arrives

}
