#### Functional

##### Inspect the game source code.

###### Was the `mini-framework` the only technology/framework used to develop the project? Any other framework or technology such as canvas or Web-GL are not allowed.

##### Try to open the game.

###### Were you asked for a nickname?

###### After entering your nickname, were you redirected to a waiting page presenting a player counter?

###### After entering your nickname, did you have access to a chat?

##### Try to enter with another user (in another browser or in private browser).

###### Did the player counter increment by 1?

###### After entering with another user, can all users chat with each other in real time (using websockets)?

##### Wait 20 seconds with two users in the waiting page.

###### After 20 seconds, did you get 10 second game start countdown?

###### After the 10 seconds did the game start?

##### Try to enter with 4 different users in the waiting page.

###### As soon as the fourth player entered, did you get 10 second game start countdown?

###### While playing the game, are you able to move and place bombs?

###### Can players see the whole map at once?

##### Try placing a bomb and standing by it when it explodes.

###### Did you lose 1 of your 3 lives?

##### Try to lose all your 3 lives.

###### Did you lose the game and are not able to play anymore?

##### Try placing a bomb next to another player.

###### Did the other player lose a life when the bomb exploded?

##### Place a bomb next to a destroyable block.

###### Did the block disappear when the bomb exploded?

##### Try to destroy every block on the map.

###### Did the power ups appear in place of some of the blocks?

###### Can you confirm that there are at least 3 types of power ups?

##### Open the Dev Tool in the Performance tab.

###### Does the game run without frame drops?

##### Open the Dev Tool in the Performance tab.

###### Does the game run at or around 60fps?

##### Open the Dev Tool in the performance tab and select the option rendering with the paint ON.

###### Is paint used as little as possible?

##### Open the Dev Tool in the performance tab and select the option rendering with the layer ON.

###### Are layers used as little as possible?

###### Are [layers being promoted](https://developers.google.com/web/fundamentals/performance/rendering/stick-to-compositor-only-properties-and-manage-layer-count) properly?

#### Bonus

###### +Is there a `Solo + Co-Op mode` in the game as described in the subject?

###### +Are 2 or more extra power ups, as described in the [subject's bonus section](../README.md), implemented?

###### +When a player dies, is a random power up release as described in the subject's bonus section?

###### +Is there a team mode implemented?

###### +Can the player interact with the game after death as described in the subject's bonus section?

###### +Do you think in general this project is well done?